package com.xm.xzp.service;

import com.github.pagehelper.PageInfo;
import com.xm.xzp.model.entity.IntTxnLog;
import com.xm.xzp.model.vo.IntTxnLogResultVo;
import com.xm.xzp.model.vo.IntTxnLogVo;

public interface IIntTxnLogService {
    PageInfo<IntTxnLog> intTxnLogList(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize);
    PageInfo<IntTxnLogResultVo> selectIntTxnLogList(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize);
    PageInfo<Object> queryTxnLogDetail(IntTxnLogVo intTxnLog, Integer pageNum, Integer pageSize);
}